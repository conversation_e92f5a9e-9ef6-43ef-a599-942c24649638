@props(['user', 'stage' => null, 'showWhiteStars' => true, 'showGreenStars' => true, 'size' => 'md'])

@php
    // Calculate user's divine lights progress
    $userStages = $user->activatedStages ?? collect();
    $totalStages = 6;
    
    // White Stars (Fulfillment) - 3 total
    $whiteStars = 0;
    if ($userStages->count() >= $totalStages) $whiteStars = 1; // All stages activated
    if ($userStages->where('green_stars', '>=', 3)->count() >= $totalStages) $whiteStars = 2; // 3 green stars in all stages
    if ($userStages->where('green_stars', '>=', 5)->count() >= $totalStages) $whiteStars = 3; // 5 green stars in all stages
    
    // Green Stars for specific stage (5 total per stage)
    $greenStars = 0;
    if ($stage) {
        $stageData = $userStages->where('stage_number', $stage)->first();
        $greenStars = $stageData->green_stars ?? 0;
    }
    
    // Size classes - Made smaller
    $sizeClasses = [
        'xs' => 'w-3 h-3',
        'sm' => 'w-3.5 h-3.5',
        'md' => 'w-4 h-4',
        'lg' => 'w-4.5 h-4.5',
        'xl' => 'w-5 h-5'
    ];
    $starSize = $sizeClasses[$size] ?? $sizeClasses['sm'];
@endphp

<div class="divine-lights-container flex items-center space-x-1">
    @if($showWhiteStars)
    <!-- White Stars (Fulfillment) -->
    <div class="white-stars flex items-center space-x-0.5" title="Fulfillment Stars">
        @for($i = 1; $i <= 3; $i++)
            <div class="star-container relative group">
                @if($i <= $whiteStars)
                    <!-- Filled White Star -->
                    <svg class="{{ $starSize }} text-white drop-shadow-lg cursor-pointer transition-transform hover:scale-110" 
                         fill="currentColor" viewBox="0 0 20 20" stroke="#d1d5db" stroke-width="1">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                @else
                    <!-- Empty White Star -->
                    <svg class="{{ $starSize }} text-gray-300 cursor-pointer transition-transform hover:scale-110" 
                         fill="none" viewBox="0 0 20 20" stroke="currentColor" stroke-width="1">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                @endif
                
                <!-- Tooltip for White Stars -->
                <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                    @if($i == 1)
                        Fulfillment Star 1: Activate all 6 stages
                    @elseif($i == 2)
                        Fulfillment Star 2: Get 3 green stars in all stages
                    @else
                        Fulfillment Star 3: Get 5 green stars in all stages
                    @endif
                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                </div>
            </div>
        @endfor
    </div>
    @endif

    @if($showGreenStars && $stage)
    <!-- Green Stars (Stage Progress) -->
    <div class="green-stars flex items-center space-x-0.5 ml-2" title="Stage {{ $stage }} Progress">
        @for($i = 1; $i <= 5; $i++)
            <div class="star-container relative group">
                @if($i <= $greenStars)
                    <!-- Filled Green Star -->
                    <svg class="{{ $starSize }} text-green-500 cursor-pointer transition-transform hover:scale-110" 
                         fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                @else
                    <!-- Empty Green Star -->
                    <svg class="{{ $starSize }} text-gray-300 cursor-pointer transition-transform hover:scale-110" 
                         fill="none" viewBox="0 0 20 20" stroke="currentColor" stroke-width="1">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                @endif
                
                <!-- Tooltip for Green Stars -->
                <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                    @if($i == 1)
                        Green Star 1: Earn 1000 points in Stage {{ $stage }}
                    @elseif($i == 2)
                        Green Star 2: Activate Stage {{ $stage }}
                    @elseif($i == 3)
                        Green Star 3: Recruit 50 people with Stage {{ $stage }} activation
                    @elseif($i == 4)
                        Green Star 4: Recruit 100 people with Stage {{ $stage }} activation
                    @else
                        Green Star 5: Recruit 160 people with Stage {{ $stage }} activation
                    @endif
                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                </div>
            </div>
        @endfor
    </div>
    @endif
</div>

@if($showWhiteStars && $showGreenStars && $stage)
<!-- Legend -->
<div class="divine-lights-legend mt-2 text-xs text-gray-600">
    <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-1">
            <svg class="w-2.5 h-2.5 text-white drop-shadow" fill="currentColor" viewBox="0 0 20 20" stroke="#d1d5db" stroke-width="1">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
            <span>Fulfillment</span>
        </div>
        <div class="flex items-center space-x-1">
            <svg class="w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
            <span>Stage Progress</span>
        </div>
    </div>
</div>
@endif

<style>
.divine-lights-container .star-container {
    position: relative;
}

.divine-lights-container .star-container:hover .tooltip {
    opacity: 1;
    visibility: visible;
}

.white-stars svg {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.green-stars svg {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}
</style>
